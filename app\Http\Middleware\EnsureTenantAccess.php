<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureTenantAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = tenant();
        
        if (!$tenant) {
            abort(404, 'Tenant not found');
        }
        
        // Check if tenant can access the system
        if (!$tenant->canAccess()) {
            return $this->handleSuspendedTenant($tenant, $request);
        }
        
        // Check if trial has expired
        if ($tenant->isOnTrial() && $tenant->trial_ends_at->isPast()) {
            return $this->handleExpiredTrial($tenant, $request);
        }
        
        return $next($request);
    }
    
    /**
     * Handle suspended tenant
     */
    private function handleSuspendedTenant($tenant, Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Tenant account is suspended',
                'status' => 'suspended',
                'tenant_id' => $tenant->id,
            ], 403);
        }
        
        return response()->view('tenant.suspended', [
            'tenant' => $tenant,
            'message' => 'Your pharmacy account has been suspended. Please contact support.',
        ], 403);
    }
    
    /**
     * Handle expired trial
     */
    private function handleExpiredTrial($tenant, Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Trial period has expired',
                'status' => 'trial_expired',
                'tenant_id' => $tenant->id,
            ], 402);
        }
        
        return response()->view('tenant.trial-expired', [
            'tenant' => $tenant,
            'message' => 'Your trial period has expired. Please upgrade to continue using the service.',
        ], 402);
    }
}
