<?php

namespace App\Services;

use App\Models\Tenant;
use App\Models\Subscription;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Exception;

class TenantService
{
    /**
     * Create a new tenant with all necessary setup
     */
    public function createTenant(array $data): Tenant
    {
        DB::beginTransaction();
        
        try {
            // Generate unique subdomain if not provided
            $subdomain = $data['subdomain'] ?? $this->generateSubdomain($data['pharmacy_name']);
            
            // Create tenant using the proper method
            $tenant = Tenant::create([
                'name' => $data['pharmacy_name'],
                'owner_name' => $data['owner_name'],
                'owner_email' => $data['owner_email'],
                'owner_phone' => $data['owner_phone'] ?? null,
                'business_license' => $data['business_license'] ?? null,
                'tax_code' => $data['tax_code'] ?? null,
                'address' => $data['address'] ?? null,
                'province' => $data['province'] ?? null,
                'district' => $data['district'] ?? null,
                'subscription_plan' => 'trial',
                'subscription_status' => 'trial',
                'trial_ends_at' => now()->addDays(14)->toISOString(),
                'settings' => $data['settings'] ?? [],
            ]);

            // Create domain for tenant
            $domain = $subdomain . '.' . config('app.domain', 'localhost');
            $tenant->domains()->create([
                'domain' => $domain,
            ]);

            // Create tenant database
            $this->createTenantDatabase($tenant);
            
            // Run tenant migrations
            $this->runTenantMigrations($tenant);
            
            // Seed initial data
            $this->seedTenantData($tenant);
            
            // Create subscription record
            $this->createSubscription($tenant);
            
            DB::commit();
            return $tenant;
            
        } catch (Exception $e) {
            DB::rollback();
            
            // Cleanup if tenant was created
            if (isset($tenant)) {
                $this->cleanupFailedTenant($tenant);
            }
            
            throw $e;
        }
    }

    /**
     * Create tenant database
     */
    private function createTenantDatabase(Tenant $tenant): void
    {
        $databaseName = config('tenancy.database.prefix') . $tenant->id;
        
        // Create database
        DB::statement("CREATE DATABASE `{$databaseName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // Update tenant with database name
        $tenant->update(['data' => array_merge($tenant->data ?? [], ['database' => $databaseName])]);
    }

    /**
     * Run tenant migrations
     */
    private function runTenantMigrations(Tenant $tenant): void
    {
        $tenant->run(function () {
            Artisan::call('migrate', [
                '--path' => 'database/migrations/tenant',
                '--force' => true,
            ]);
        });
    }

    /**
     * Seed tenant data
     */
    private function seedTenantData(Tenant $tenant): void
    {
        $tenant->run(function () {
            // Seed basic data like roles, permissions, default settings
            Artisan::call('db:seed', [
                '--class' => 'TenantSeeder',
                '--force' => true,
            ]);
        });
    }

    /**
     * Create subscription record
     */
    private function createSubscription(Tenant $tenant): void
    {
        Subscription::create([
            'tenant_id' => $tenant->id,
            'plan' => 'trial',
            'status' => 'trial',
            'price' => 0,
            'billing_cycle' => 'monthly',
            'starts_at' => now(),
            'trial_ends_at' => $tenant->trial_ends_at,
            'features' => $this->getTrialFeatures(),
        ]);
    }

    /**
     * Generate unique subdomain
     */
    private function generateSubdomain(string $pharmacyName): string
    {
        $base = Str::slug($pharmacyName);
        $subdomain = $base;
        $counter = 1;
        
        while ($this->subdomainExists($subdomain)) {
            $subdomain = $base . '-' . $counter;
            $counter++;
        }
        
        return $subdomain;
    }

    /**
     * Check if subdomain exists
     */
    private function subdomainExists(string $subdomain): bool
    {
        $domain = $subdomain . '.' . config('app.domain', 'localhost');
        return DB::table('domains')->where('domain', $domain)->exists();
    }

    /**
     * Get trial features
     */
    private function getTrialFeatures(): array
    {
        return [
            'max_users' => 3,
            'max_products' => 100,
            'inventory_management' => true,
            'basic_reports' => true,
            'customer_management' => true,
            'invoice_generation' => true,
            'email_support' => true,
        ];
    }

    /**
     * Suspend tenant
     */
    public function suspendTenant(Tenant $tenant, string $reason = ''): void
    {
        DB::beginTransaction();

        try {
            // Update tenant subscription status
            $tenant->update(['subscription_status' => 'suspended']);

            $tenant->subscription()->update([
                'status' => 'suspended',
                'canceled_at' => now(),
            ]);

            // Optionally backup tenant database
            $this->backupTenantDatabase($tenant);

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Reactivate tenant
     */
    public function reactivateTenant(Tenant $tenant): void
    {
        DB::beginTransaction();

        try {
            // Update tenant subscription status
            $tenant->update(['subscription_status' => 'active']);

            $tenant->subscription()->update([
                'status' => 'active',
                'canceled_at' => null,
                'ends_at' => now()->addMonth(),
            ]);

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Delete tenant completely
     */
    public function deleteTenant(Tenant $tenant): void
    {
        DB::beginTransaction();
        
        try {
            // Backup before deletion
            $this->backupTenantDatabase($tenant);
            
            // Drop tenant database
            $databaseName = $tenant->data['database'] ?? config('tenancy.database.prefix') . $tenant->id;
            DB::statement("DROP DATABASE IF EXISTS `{$databaseName}`");
            
            // Delete subscription
            $tenant->subscription()->delete();
            
            // Delete tenant record (domains will be deleted via cascade)
            $tenant->delete();
            
            DB::commit();
            
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Backup tenant database
     */
    private function backupTenantDatabase(Tenant $tenant): void
    {
        // Implementation for database backup
        // This could use mysqldump or other backup tools
        // For now, we'll just log the action
        \Log::info("Backup requested for tenant: {$tenant->id}");
    }

    /**
     * Cleanup failed tenant creation
     */
    private function cleanupFailedTenant(Tenant $tenant): void
    {
        try {
            // Drop database if it was created
            $databaseName = config('tenancy.database.prefix') . $tenant->id;
            DB::statement("DROP DATABASE IF EXISTS `{$databaseName}`");
            
            // Delete tenant record
            $tenant->delete();
            
        } catch (Exception $e) {
            \Log::error("Failed to cleanup tenant {$tenant->id}: " . $e->getMessage());
        }
    }
}
