[2025-06-10 05:45:52] local.ERROR: could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(522): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(59): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(28): Illuminate\\Database\\SQLiteConnection->configureForeignKeyConstraints()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(277): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'D:\\\\laragon\\\\www\\\\...', '', Array)
#6 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'D:\\\\laragon\\\\www\\\\...', '', Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(51): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(207): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(103): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#10 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(165): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(165): retry(1, Object(Closure), 0, Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(141): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#17 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(111): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#18 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#27 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#35 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('sqlite:D:\\\\larag...', NULL, Object(SensitiveParameterValue), Array)
#1 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:D:\\\\larag...', NULL, Object(SensitiveParameterValue), Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(40): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:D:\\\\larag...', Array, Array)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(565): Illuminate\\Database\\Connection->getPdo()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(522): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(59): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(28): Illuminate\\Database\\SQLiteConnection->configureForeignKeyConstraints()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(277): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'D:\\\\laragon\\\\www\\\\...', '', Array)
#14 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'D:\\\\laragon\\\\www\\\\...', '', Array)
#15 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(51): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(207): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#17 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(103): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#18 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#19 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(165): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#23 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(165): retry(1, Object(Closure), 0, Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(141): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#25 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(111): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#26 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#27 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#29 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#30 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#31 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#34 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#35 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\pharmacy-saas\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#43 {main}
"} 
[2025-06-10 05:50:38] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'pharmasaas'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `sessions` where `id` = bVCS3s2yw1SUtKcz7pjqyehTM0rQiUBpRj3qzQv5 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'pharmasaas'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `sessions` where `id` = bVCS3s2yw1SUtKcz7pjqyehTM0rQiUBpRj3qzQv5 limit 1) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('bVCS3s2yw1SUtKc...')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('bVCS3s2yw1SUtKc...')
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(89): Illuminate\\Session\\Store->loadSession()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'pharmasaas'@'localhost' (using password: YES) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'pharmasaas', Object(SensitiveParameterValue), Array)
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'pharmasaas', Object(SensitiveParameterValue), Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('bVCS3s2yw1SUtKc...')
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('bVCS3s2yw1SUtKc...')
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(89): Illuminate\\Session\\Store->loadSession()
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 {main}
"} 
[2025-06-10 05:50:50] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = hqOien6Upxp1huWonDjLsiYg2CO1Q2AvtjBKmaTk limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = hqOien6Upxp1huWonDjLsiYg2CO1Q2AvtjBKmaTk limit 1) at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('hqOien6Upxp1huW...')
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('hqOien6Upxp1huW...')
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(89): Illuminate\\Session\\Store->loadSession()
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'pharmasaas_central.sessions' doesn't exist at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:407)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): PDO->prepare('select * from `...')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('hqOien6Upxp1huW...')
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('hqOien6Upxp1huW...')
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(89): Illuminate\\Session\\Store->loadSession()
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 {main}
"} 
[2025-06-10 06:18:23] local.ERROR: require(D:\laragon\www\pmql-hieuthuoc\routes/tenant.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(D:\\laragon\\www\\pmql-hieuthuoc\\routes/tenant.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php:35)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 35)
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 35)
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(513): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes('D:\\\\laragon\\\\www\\\\...')
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(207): Illuminate\\Routing\\Router->group(Array, 'D:\\\\laragon\\\\www\\\\...')
#6 D:\\laragon\\www\\pmql-hieuthuoc\\bootstrap\\app.php(17): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\laragon\\\\www\\\\...')
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(263): {closure}(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call(Object(Closure))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(144): Illuminate\\Container\\Container->call(Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1154): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider))
#23 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider), 'Illuminate\\\\Foun...')
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}
"} 
[2025-06-10 13:28:07] local.ERROR: Tenant could not be identified on domain pmql-hieuthuoc.test {"exception":"[object] (Stancl\\Tenancy\\Exceptions\\TenantCouldNotBeIdentifiedOnDomainException(code: 0): Tenant could not be identified on domain pmql-hieuthuoc.test at D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\DomainTenantResolver.php:48)
[stacktrace]
#0 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Resolvers\\Contracts\\CachedTenantResolver.php(34): Stancl\\Tenancy\\Resolvers\\DomainTenantResolver->resolveWithoutCache('pmql-hieuthuoc....')
#1 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\IdentificationMiddleware.php(26): Stancl\\Tenancy\\Resolvers\\Contracts\\CachedTenantResolver->resolve('pmql-hieuthuoc....')
#2 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\InitializeTenancyByDomain.php(37): Stancl\\Tenancy\\Middleware\\IdentificationMiddleware->initializeTenancy(Object(Illuminate\\Http\\Request), Object(Closure), 'pmql-hieuthuoc....')
#3 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\InitializeTenancyByDomain->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\stancl\\tenancy\\src\\Middleware\\PreventAccessFromCentralDomains.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Stancl\\Tenancy\\Middleware\\PreventAccessFromCentralDomains->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\pmql-hieuthuoc\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\pmql-hieuthuoc\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 {main}
"} 
