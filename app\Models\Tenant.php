<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains, HasFactory;

    // Relationships
    public function subscription()
    {
        return $this->hasOne(Subscription::class);
    }

    // Accessors for data stored in JSON column
    public function getNameAttribute()
    {
        return $this->data['name'] ?? null;
    }

    public function getOwnerNameAttribute()
    {
        return $this->data['owner_name'] ?? null;
    }

    public function getOwnerEmailAttribute()
    {
        return $this->data['owner_email'] ?? null;
    }

    public function getOwnerPhoneAttribute()
    {
        return $this->data['owner_phone'] ?? null;
    }

    public function getBusinessLicenseAttribute()
    {
        return $this->data['business_license'] ?? null;
    }

    public function getTaxCodeAttribute()
    {
        return $this->data['tax_code'] ?? null;
    }

    public function getAddressAttribute()
    {
        return $this->data['address'] ?? null;
    }

    public function getProvinceAttribute()
    {
        return $this->data['province'] ?? null;
    }

    public function getDistrictAttribute()
    {
        return $this->data['district'] ?? null;
    }

    public function getSubscriptionPlanAttribute()
    {
        return $this->data['subscription_plan'] ?? 'trial';
    }

    public function getSubscriptionStatusAttribute()
    {
        return $this->data['subscription_status'] ?? 'trial';
    }

    public function getTrialEndsAtAttribute()
    {
        $date = $this->data['trial_ends_at'] ?? null;
        return $date ? \Carbon\Carbon::parse($date) : null;
    }

    public function getSettingsAttribute()
    {
        return $this->data['settings'] ?? [];
    }

    // Business methods
    public function isActive(): bool
    {
        return $this->subscription_status === 'active';
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    public function isSuspended(): bool
    {
        return $this->subscription_status === 'suspended';
    }

    public function canAccess(): bool
    {
        return $this->isActive() || $this->isOnTrial();
    }

    public function getSubdomainAttribute(): string
    {
        $domain = $this->domains()->first();
        if ($domain) {
            return explode('.', $domain->domain)[0];
        }
        return '';
    }

    public function getFullDomainAttribute(): string
    {
        $domain = $this->domains()->first();
        return $domain ? $domain->domain : '';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'active');
    }

    public function scopeOnTrial($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'trial')
                    ->where('data->trial_ends_at', '>', now());
    }

    public function scopeExpiredTrial($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'trial')
                    ->where('data->trial_ends_at', '<=', now());
    }

    public function scopeSuspended($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'suspended');
    }
}
