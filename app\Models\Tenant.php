<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains, HasFactory;

    // Relationships
    public function subscription()
    {
        return $this->hasOne(Subscription::class);
    }

    // Use the data column methods from HasDataColumn trait
    public function getNameAttribute()
    {
        return $this->getCustomColumn('name');
    }

    public function getOwnerNameAttribute()
    {
        return $this->getCustomColumn('owner_name');
    }

    public function getOwnerEmailAttribute()
    {
        return $this->getCustomColumn('owner_email');
    }

    public function getOwnerPhoneAttribute()
    {
        return $this->getCustomColumn('owner_phone');
    }

    public function getBusinessLicenseAttribute()
    {
        return $this->getCustomColumn('business_license');
    }

    public function getTaxCodeAttribute()
    {
        return $this->getCustomColumn('tax_code');
    }

    public function getAddressAttribute()
    {
        return $this->getCustomColumn('address');
    }

    public function getProvinceAttribute()
    {
        return $this->getCustomColumn('province');
    }

    public function getDistrictAttribute()
    {
        return $this->getCustomColumn('district');
    }

    public function getSubscriptionPlanAttribute()
    {
        return $this->getCustomColumn('subscription_plan', 'trial');
    }

    public function getSubscriptionStatusAttribute()
    {
        return $this->getCustomColumn('subscription_status', 'trial');
    }

    public function getTrialEndsAtAttribute()
    {
        $date = $this->getCustomColumn('trial_ends_at');
        return $date ? \Carbon\Carbon::parse($date) : null;
    }

    public function getSettingsAttribute()
    {
        return $this->getCustomColumn('settings', []);
    }

    // Business methods
    public function isActive(): bool
    {
        return $this->subscription_status === 'active';
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    public function isSuspended(): bool
    {
        return $this->subscription_status === 'suspended';
    }

    public function canAccess(): bool
    {
        return $this->isActive() || $this->isOnTrial();
    }

    public function getSubdomainAttribute(): string
    {
        $domain = $this->domains()->first();
        if ($domain) {
            return explode('.', $domain->domain)[0];
        }
        return '';
    }

    public function getFullDomainAttribute(): string
    {
        $domain = $this->domains()->first();
        return $domain ? $domain->domain : '';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'active');
    }

    public function scopeOnTrial($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'trial')
                    ->where('data->trial_ends_at', '>', now());
    }

    public function scopeExpiredTrial($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'trial')
                    ->where('data->trial_ends_at', '<=', now());
    }

    public function scopeSuspended($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'suspended');
    }
}
