<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains, HasFactory;

    // Override to specify which columns are actual database columns
    public static function getCustomColumns(): array
    {
        return [
            'id',
            'created_at',
            'updated_at',
            'data',
        ];
    }

    // Relationships
    public function subscription()
    {
        return $this->hasOne(Subscription::class);
    }

    // Accessor for trial_ends_at to handle string dates
    public function getTrialEndsAtAttribute()
    {
        $date = $this->attributes['trial_ends_at'] ?? null;
        if (is_string($date)) {
            return \Carbon\Carbon::parse($date);
        }
        return $date;
    }

    // Business methods
    public function isActive(): bool
    {
        return $this->subscription_status === 'active';
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    public function isSuspended(): bool
    {
        return $this->subscription_status === 'suspended';
    }

    public function canAccess(): bool
    {
        return $this->isActive() || $this->isOnTrial();
    }

    public function getSubdomainAttribute(): string
    {
        $domain = $this->domains()->first();
        if ($domain) {
            return explode('.', $domain->domain)[0];
        }
        return '';
    }

    public function getFullDomainAttribute(): string
    {
        $domain = $this->domains()->first();
        return $domain ? $domain->domain : '';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'active');
    }

    public function scopeOnTrial($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'trial')
                    ->where('data->trial_ends_at', '>', now());
    }

    public function scopeExpiredTrial($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'trial')
                    ->where('data->trial_ends_at', '<=', now());
    }

    public function scopeSuspended($query)
    {
        return $query->whereJsonContains('data->subscription_status', 'suspended');
    }
}
