<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'plan',
        'status',
        'price',
        'billing_cycle',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'canceled_at',
        'features',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'canceled_at' => 'datetime',
        'price' => 'decimal:2',
        'features' => 'array',
    ];

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    // Business methods
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               $this->ends_at && 
               $this->ends_at->isFuture();
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && 
               $this->trial_ends_at->isFuture();
    }

    public function isExpired(): bool
    {
        return $this->ends_at && $this->ends_at->isPast();
    }

    public function isCanceled(): bool
    {
        return $this->status === 'canceled';
    }

    public function daysUntilExpiry(): int
    {
        if (!$this->ends_at) {
            return 0;
        }
        
        return max(0, now()->diffInDays($this->ends_at, false));
    }

    public function daysUntilTrialExpiry(): int
    {
        if (!$this->trial_ends_at) {
            return 0;
        }
        
        return max(0, now()->diffInDays($this->trial_ends_at, false));
    }

    // Plan features
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    public function getMaxUsers(): int
    {
        return match($this->plan) {
            'trial' => 3,
            'basic' => 5,
            'premium' => 20,
            'enterprise' => 100,
            default => 1,
        };
    }

    public function getMaxProducts(): int
    {
        return match($this->plan) {
            'trial' => 100,
            'basic' => 1000,
            'premium' => 10000,
            'enterprise' => -1, // unlimited
            default => 50,
        };
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('ends_at', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('ends_at', '<=', now());
    }

    public function scopeOnTrial($query)
    {
        return $query->where('trial_ends_at', '>', now());
    }

    public function scopeExpiredTrial($query)
    {
        return $query->where('trial_ends_at', '<=', now());
    }
}
