<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get tenant information from central database
        $tenant = tenant();

        if (!$tenant) {
            $this->command->error('No tenant context found');
            return;
        }

        // Create owner user for this tenant
        User::create([
            'name' => $tenant->owner_name,
            'email' => $tenant->owner_email,
            'password' => Hash::make('password'), // Default password, should be changed
            'phone' => $tenant->owner_phone,
            'role' => 'owner',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $this->command->info('Tenant seeding completed for: ' . $tenant->name);
    }
}
