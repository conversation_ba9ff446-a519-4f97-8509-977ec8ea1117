<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Management System - Tenants</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">🏥 Pharmacy Management System</h1>
            <p class="text-gray-600 mb-8">Chọn nhà thuốc để truy cập hệ thống quản lý:</p>
            
            <?php if($tenants->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-6 border border-blue-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center mb-4">
                                <div class="bg-blue-500 text-white rounded-full p-2 mr-3">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <h2 class="text-xl font-semibold text-gray-900"><?php echo e($tenant->name); ?></h2>
                            </div>
                            
                            <div class="space-y-2 mb-4">
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Chủ sở hữu:</span> <?php echo e($tenant->owner_name); ?>

                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Email:</span> <?php echo e($tenant->owner_email); ?>

                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Gói:</span> 
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php echo e($tenant->subscription_plan === 'trial' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'); ?>">
                                        <?php echo e(ucfirst($tenant->subscription_plan)); ?>

                                    </span>
                                </p>
                                <?php if($tenant->trial_ends_at): ?>
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">Trial kết thúc:</span> 
                                        <?php echo e($tenant->trial_ends_at->format('d/m/Y')); ?>

                                    </p>
                                <?php endif; ?>
                            </div>
                            
                            <div class="flex space-x-2">
                                <a href="<?php echo e(url('/tenant/' . $tenant->id)); ?>" 
                                   class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition duration-200">
                                    Xem Dashboard
                                </a>
                                <a href="<?php echo e(url('/tenant/' . $tenant->id . '/admin')); ?>" 
                                   class="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-md hover:bg-green-700 transition duration-200">
                                    Vào Admin
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 mb-4">
                        <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có nhà thuốc nào</h3>
                    <p class="text-gray-600">Hãy tạo tenant đầu tiên bằng command line.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
<?php /**PATH D:\laragon\www\pmql-hieuthuoc\resources\views/tenants/list.blade.php ENDPATH**/ ?>