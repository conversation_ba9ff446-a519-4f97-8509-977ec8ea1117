<?php

namespace App\Jobs;

use App\Models\Tenant;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class CheckExpiredTrials implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Checking for expired trials...');

        // Find tenants with expired trials
        $expiredTrials = Tenant::where('subscription_status', 'trial')
            ->where('trial_ends_at', '<', now())
            ->get();

        foreach ($expiredTrials as $tenant) {
            $this->handleExpiredTrial($tenant);
        }

        // Find tenants with trials expiring in 3 days
        $expiringTrials = Tenant::where('subscription_status', 'trial')
            ->whereBetween('trial_ends_at', [now(), now()->addDays(3)])
            ->get();

        foreach ($expiringTrials as $tenant) {
            $this->handleExpiringTrial($tenant);
        }

        Log::info("Processed {$expiredTrials->count()} expired trials and {$expiringTrials->count()} expiring trials");
    }

    /**
     * Handle expired trial
     */
    private function handleExpiredTrial(Tenant $tenant): void
    {
        // Update tenant status
        $tenant->update(['subscription_status' => 'suspended']);

        // Update subscription status
        $tenant->subscription()->update(['status' => 'expired']);

        // Send notification email
        try {
            // Mail::to($tenant->owner_email)
            //     ->send(new TrialExpiredMail($tenant));

            Log::info("Trial expired notification sent to {$tenant->owner_email}");
        } catch (\Exception $e) {
            Log::error("Failed to send trial expired email to {$tenant->owner_email}: " . $e->getMessage());
        }
    }

    /**
     * Handle expiring trial (warning)
     */
    private function handleExpiringTrial(Tenant $tenant): void
    {
        $daysLeft = now()->diffInDays($tenant->trial_ends_at);

        // Send warning email
        try {
            // Mail::to($tenant->owner_email)
            //     ->send(new TrialExpiringMail($tenant, $daysLeft));

            Log::info("Trial expiring warning sent to {$tenant->owner_email} ({$daysLeft} days left)");
        } catch (\Exception $e) {
            Log::error("Failed to send trial expiring email to {$tenant->owner_email}: " . $e->getMessage());
        }
    }
}
