<?php

namespace App\Console\Commands;

use App\Services\TenantService;
use Illuminate\Console\Command;
use Exception;

class CreateTenantCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:create
                            {subdomain : The subdomain for the tenant}
                            {--owner= : Owner name}
                            {--email= : Owner email}
                            {--phone= : Owner phone}
                            {--pharmacy= : Pharmacy name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new tenant with database and initial setup';

    /**
     * Execute the console command.
     */
    public function handle(TenantService $tenantService)
    {
        $subdomain = $this->argument('subdomain');

        // Get required information
        $pharmacyName = $this->option('pharmacy') ?: $this->ask('Pharmacy name');
        $ownerName = $this->option('owner') ?: $this->ask('Owner name');
        $ownerEmail = $this->option('email') ?: $this->ask('Owner email');
        $ownerPhone = $this->option('phone') ?: $this->ask('Owner phone (optional)', null);

        // Validate email
        if (!filter_var($ownerEmail, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email address');
            return 1;
        }

        $this->info('Creating tenant...');

        try {
            $tenant = $tenantService->createTenant([
                'subdomain' => $subdomain,
                'pharmacy_name' => $pharmacyName,
                'owner_name' => $ownerName,
                'owner_email' => $ownerEmail,
                'owner_phone' => $ownerPhone,
            ]);

            $this->info('Tenant created successfully!');
            $this->table(['Field', 'Value'], [
                ['ID', $tenant->id],
                ['Name', $tenant->name],
                ['Domain', $tenant->full_domain],
                ['Owner', $tenant->owner_name],
                ['Email', $tenant->owner_email],
                ['Status', $tenant->subscription_status],
                ['Trial Ends', $tenant->trial_ends_at ? $tenant->trial_ends_at->format('Y-m-d H:i:s') : 'N/A'],
            ]);

            return 0;

        } catch (Exception $e) {
            $this->error('Failed to create tenant: ' . $e->getMessage());
            return 1;
        }
    }
}
