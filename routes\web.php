<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Central domain routes
Route::prefix('tenant/{tenant_id}')->group(function () {
    Route::get('/', function ($tenantId) {
        $tenant = \App\Models\Tenant::find($tenantId);
        if (!$tenant) {
            abort(404, 'Tenant not found');
        }

        // Initialize tenancy manually
        tenancy()->initialize($tenant);

        return redirect('/admin');
    });
});
